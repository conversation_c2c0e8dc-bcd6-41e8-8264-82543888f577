<!DOCTYPE html>
<html>
  <head>
    <title>Audio System Diagnostic Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .test-section {
        background: white;
        margin: 20px 0;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 5px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .test-button:hover {
        background: #0056b3;
      }
      .test-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        font-family: monospace;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .log-container {
        max-height: 300px;
        overflow-y: auto;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 10px;
        font-family: monospace;
        font-size: 12px;
      }
      .log-entry {
        margin: 2px 0;
        padding: 2px 5px;
      }
      .log-entry.error {
        background: #f8d7da;
        color: #721c24;
      }
      .log-entry.success {
        background: #d4edda;
        color: #155724;
      }
      .log-entry.warning {
        background: #fff3cd;
        color: #856404;
      }
      h2 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }
      .metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 10px 0;
      }
      .metric {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        text-align: center;
      }
      .metric-value {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
      }
      .metric-label {
        font-size: 12px;
        color: #6c757d;
      }
    </style>
  </head>
  <body>
    <h1>🔊 Audio System Diagnostic Test</h1>
    <p>
      This comprehensive test will diagnose audio input/output issues in the
      Gemini Live application.
    </p>

    <!-- Basic Audio Context Test -->
    <div class="test-section">
      <h2>1. Basic Audio Context Test</h2>
      <button class="test-button" onclick="testAudioContext()">
        Test Audio Context
      </button>
      <button class="test-button" onclick="testAudioOutput()">
        Test Audio Output (440Hz Tone)
      </button>
      <div id="audioContextStatus" class="status info">Ready to test...</div>
    </div>

    <!-- Microphone Access Test -->
    <div class="test-section">
      <h2>2. Microphone Access Test</h2>
      <button class="test-button" onclick="testMicrophoneAccess()">
        Test Microphone Access
      </button>
      <button class="test-button" onclick="testMicrophoneRecording()">
        Test Recording & Playback
      </button>
      <div id="microphoneStatus" class="status info">Ready to test...</div>
      <div class="metrics">
        <div class="metric">
          <div class="metric-value" id="micSampleRate">-</div>
          <div class="metric-label">Sample Rate</div>
        </div>
        <div class="metric">
          <div class="metric-value" id="micChannels">-</div>
          <div class="metric-label">Channels</div>
        </div>
        <div class="metric">
          <div class="metric-value" id="micLatency">-</div>
          <div class="metric-label">Latency (ms)</div>
        </div>
      </div>
    </div>

    <!-- WebSocket Connection Test -->
    <div class="test-section">
      <h2>3. WebSocket Connection Test</h2>
      <button class="test-button" onclick="testWebSocketConnection()">
        Test WebSocket Connection
      </button>
      <button class="test-button" onclick="testAudioTransmission()">
        Test Audio Transmission
      </button>
      <div id="websocketStatus" class="status info">Ready to test...</div>
    </div>

    <!-- AudioWorklet Test -->
    <div class="test-section">
      <h2>4. AudioWorklet Support Test</h2>
      <button class="test-button" onclick="testAudioWorkletSupport()">
        Test AudioWorklet Support
      </button>
      <button class="test-button" onclick="testEnhancedAudioProcessor()">
        Test Enhanced Audio Processor
      </button>
      <div id="audioWorkletStatus" class="status info">Ready to test...</div>
    </div>

    <!-- Buffer Management Test -->
    <div class="test-section">
      <h2>5. Buffer Management Test</h2>
      <button class="test-button" onclick="testBufferManagement()">
        Test Buffer Management
      </button>
      <div id="bufferStatus" class="status info">Ready to test...</div>
      <div class="metrics">
        <div class="metric">
          <div class="metric-value" id="bufferFillLevel">-</div>
          <div class="metric-label">Fill Level (%)</div>
        </div>
        <div class="metric">
          <div class="metric-value" id="bufferOverruns">-</div>
          <div class="metric-label">Overruns</div>
        </div>
        <div class="metric">
          <div class="metric-value" id="bufferUnderruns">-</div>
          <div class="metric-label">Underruns</div>
        </div>
      </div>
    </div>

    <!-- Network Resilience Test -->
    <div class="test-section">
      <h2>6. Network Resilience Test</h2>
      <button class="test-button" onclick="testNetworkResilience()">
        Test Network Resilience
      </button>
      <div id="networkStatus" class="status info">Ready to test...</div>
    </div>

    <!-- Comprehensive Integration Test -->
    <div class="test-section">
      <h2>7. Integration Test</h2>
      <button class="test-button" onclick="runComprehensiveTest()">
        Run Full Integration Test
      </button>
      <div id="integrationStatus" class="status info">Ready to test...</div>
    </div>

    <!-- Test Log -->
    <div class="test-section">
      <h2>Test Log</h2>
      <button class="test-button" onclick="clearLog()">Clear Log</button>
      <button class="test-button" onclick="exportLog()">Export Log</button>
      <div id="testLog" class="log-container"></div>
    </div>

    <script>
      let testLog = [];
      let audioContext = null;
      let mediaStream = null;
      let testWebSocket = null;

      function log(message, type = "info") {
        const timestamp = new Date().toISOString();
        const entry = { timestamp, message, type };
        testLog.push(entry);

        const logContainer = document.getElementById("testLog");
        const logEntry = document.createElement("div");
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;

        console.log(`[AUDIO_DIAGNOSTIC] ${message}`);
      }

      function updateStatus(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        element.textContent = message;
        element.className = `status ${type}`;
      }

      function clearLog() {
        testLog = [];
        document.getElementById("testLog").innerHTML = "";
      }

      function exportLog() {
        const logText = testLog
          .map(
            (entry) =>
              `[${entry.timestamp}] [${entry.type.toUpperCase()}] ${
                entry.message
              }`
          )
          .join("\n");

        const blob = new Blob([logText], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `audio-diagnostic-${Date.now()}.log`;
        a.click();
        URL.revokeObjectURL(url);
      }

      // Test 1: Audio Context
      async function testAudioContext() {
        log("Testing Audio Context creation...", "info");
        try {
          if (audioContext) {
            audioContext.close();
          }

          audioContext = new (window.AudioContext || window.webkitAudioContext)(
            {
              sampleRate: 24000,
            }
          );

          log(
            `Audio Context created: state=${audioContext.state}, sampleRate=${audioContext.sampleRate}`,
            "success"
          );

          if (audioContext.state === "suspended") {
            log("Audio Context suspended, attempting to resume...", "info");
            await audioContext.resume();
            log(
              `Audio Context resumed: state=${audioContext.state}`,
              "success"
            );
          }

          updateStatus(
            "audioContextStatus",
            `Audio Context: ${audioContext.state} (${audioContext.sampleRate}Hz)`,
            "success"
          );
          return true;
        } catch (error) {
          log(`Audio Context test failed: ${error.message}`, "error");
          updateStatus(
            "audioContextStatus",
            `Failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      async function testAudioOutput() {
        log("Testing audio output with 440Hz tone...", "info");
        try {
          if (!audioContext) {
            const contextOk = await testAudioContext();
            if (!contextOk) return false;
          }

          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.type = "sine";
          oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.start();
          log("440Hz tone started", "success");

          setTimeout(() => {
            oscillator.stop();
            oscillator.disconnect();
            gainNode.disconnect();
            log("440Hz tone stopped", "success");
          }, 1000);

          updateStatus(
            "audioContextStatus",
            "Audio output test completed successfully",
            "success"
          );
          return true;
        } catch (error) {
          log(`Audio output test failed: ${error.message}`, "error");
          updateStatus(
            "audioContextStatus",
            `Output test failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      // Test 2: Microphone Access
      async function testMicrophoneAccess() {
        log("Testing microphone access...", "info");
        try {
          const constraints = {
            audio: {
              sampleRate: 16000,
              channelCount: 1,
              echoCancellation: true,
              noiseSuppression: true,
            },
          };

          mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
          log("Microphone access granted", "success");

          const audioTracks = mediaStream.getAudioTracks();
          if (audioTracks.length > 0) {
            const track = audioTracks[0];
            const settings = track.getSettings();

            log(
              `Microphone settings: sampleRate=${settings.sampleRate}, channels=${settings.channelCount}`,
              "info"
            );

            document.getElementById("micSampleRate").textContent =
              settings.sampleRate || "Unknown";
            document.getElementById("micChannels").textContent =
              settings.channelCount || "Unknown";

            updateStatus(
              "microphoneStatus",
              "Microphone access successful",
              "success"
            );
            return true;
          }
        } catch (error) {
          log(`Microphone access failed: ${error.message}`, "error");
          updateStatus("microphoneStatus", `Failed: ${error.message}`, "error");
          return false;
        }
      }

      async function testMicrophoneRecording() {
        log("Testing microphone recording and playback...", "info");
        try {
          if (!mediaStream) {
            const micOk = await testMicrophoneAccess();
            if (!micOk) return false;
          }

          if (!audioContext) {
            const contextOk = await testAudioContext();
            if (!contextOk) return false;
          }

          const source = audioContext.createMediaStreamSource(mediaStream);
          const analyser = audioContext.createAnalyser();
          analyser.fftSize = 256;

          source.connect(analyser);

          const dataArray = new Uint8Array(analyser.frequencyBinCount);
          let maxLevel = 0;
          let sampleCount = 0;

          const checkAudio = () => {
            analyser.getByteFrequencyData(dataArray);
            const level = Math.max(...dataArray);
            maxLevel = Math.max(maxLevel, level);
            sampleCount++;

            if (sampleCount < 100) {
              requestAnimationFrame(checkAudio);
            } else {
              log(
                `Recording test completed: max level=${maxLevel}`,
                maxLevel > 10 ? "success" : "warning"
              );
              updateStatus(
                "microphoneStatus",
                maxLevel > 10
                  ? "Recording test successful"
                  : "Low audio level detected",
                maxLevel > 10 ? "success" : "warning"
              );

              source.disconnect();
            }
          };

          checkAudio();
          return true;
        } catch (error) {
          log(`Recording test failed: ${error.message}`, "error");
          updateStatus(
            "microphoneStatus",
            `Recording test failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      // Test 3: WebSocket Connection
      async function testWebSocketConnection() {
        log("Testing WebSocket connection...", "info");
        try {
          if (testWebSocket) {
            testWebSocket.close();
          }

          testWebSocket = new WebSocket(
            "ws://localhost:8000/listen?lang=en-US"
          );
          testWebSocket.binaryType = "arraybuffer";

          return new Promise((resolve) => {
            const timeout = setTimeout(() => {
              log("WebSocket connection timeout", "error");
              updateStatus("websocketStatus", "Connection timeout", "error");
              resolve(false);
            }, 5000);

            testWebSocket.onopen = () => {
              clearTimeout(timeout);
              log("WebSocket connection established", "success");
              updateStatus(
                "websocketStatus",
                "WebSocket connected successfully",
                "success"
              );
              resolve(true);
            };

            testWebSocket.onerror = (error) => {
              clearTimeout(timeout);
              log(`WebSocket connection error: ${error}`, "error");
              updateStatus(
                "websocketStatus",
                "WebSocket connection failed",
                "error"
              );
              resolve(false);
            };

            testWebSocket.onmessage = (event) => {
              log(`WebSocket message received: ${typeof event.data}`, "info");
            };
          });
        } catch (error) {
          log(`WebSocket test failed: ${error.message}`, "error");
          updateStatus("websocketStatus", `Failed: ${error.message}`, "error");
          return false;
        }
      }

      async function testAudioTransmission() {
        log("Testing audio transmission over WebSocket...", "info");
        try {
          if (!testWebSocket || testWebSocket.readyState !== WebSocket.OPEN) {
            const wsOk = await testWebSocketConnection();
            if (!wsOk) return false;
          }

          // Create test audio data (1 second of 440Hz sine wave at 16kHz)
          const sampleRate = 16000;
          const duration = 1; // seconds
          const samples = sampleRate * duration;
          const audioData = new Float32Array(samples);

          for (let i = 0; i < samples; i++) {
            audioData[i] = Math.sin((2 * Math.PI * 440 * i) / sampleRate) * 0.3;
          }

          // Convert to PCM16 bytes
          const pcmData = new Int16Array(samples);
          for (let i = 0; i < samples; i++) {
            pcmData[i] = Math.max(
              -32768,
              Math.min(32767, audioData[i] * 32767)
            );
          }

          testWebSocket.send(pcmData.buffer);
          log(
            `Audio data transmitted: ${pcmData.buffer.byteLength} bytes`,
            "success"
          );
          updateStatus(
            "websocketStatus",
            "Audio transmission test completed",
            "success"
          );
          return true;
        } catch (error) {
          log(`Audio transmission test failed: ${error.message}`, "error");
          updateStatus(
            "websocketStatus",
            `Transmission failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      // Test 4: AudioWorklet Support
      async function testAudioWorkletSupport() {
        log("Testing AudioWorklet support...", "info");
        try {
          if (!audioContext) {
            const contextOk = await testAudioContext();
            if (!contextOk) return false;
          }

          if (typeof AudioWorkletNode === "undefined") {
            log("AudioWorklet not supported in this browser", "warning");
            updateStatus(
              "audioWorkletStatus",
              "AudioWorklet not supported - will use fallback",
              "warning"
            );
            return false;
          }

          // Test loading the enhanced audio processor
          try {
            await audioContext.audioWorklet.addModule(
              "/enhanced-audio-processor.js"
            );
            log("Enhanced audio processor loaded successfully", "success");
            updateStatus(
              "audioWorkletStatus",
              "AudioWorklet supported and processor loaded",
              "success"
            );
            return true;
          } catch (moduleError) {
            log(
              `Failed to load audio processor: ${moduleError.message}`,
              "warning"
            );
            updateStatus(
              "audioWorkletStatus",
              "AudioWorklet supported but processor failed to load",
              "warning"
            );
            return false;
          }
        } catch (error) {
          log(`AudioWorklet test failed: ${error.message}`, "error");
          updateStatus(
            "audioWorkletStatus",
            `Failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      async function testEnhancedAudioProcessor() {
        log("Testing enhanced audio processor...", "info");
        try {
          const workletSupported = await testAudioWorkletSupport();
          if (!workletSupported) {
            log(
              "Skipping enhanced processor test - AudioWorklet not available",
              "warning"
            );
            return false;
          }

          if (!mediaStream) {
            const micOk = await testMicrophoneAccess();
            if (!micOk) return false;
          }

          const source = audioContext.createMediaStreamSource(mediaStream);
          const processorNode = new AudioWorkletNode(
            audioContext,
            "enhanced-audio-processor"
          );

          source.connect(processorNode);

          let messageCount = 0;
          processorNode.port.onmessage = (event) => {
            messageCount++;
            log(
              `Processor message ${messageCount}: ${event.data.type}`,
              "info"
            );

            if (messageCount >= 5) {
              source.disconnect();
              processorNode.disconnect();
              log("Enhanced audio processor test completed", "success");
              updateStatus(
                "audioWorkletStatus",
                "Enhanced processor working correctly",
                "success"
              );
            }
          };

          // Send test configuration
          processorNode.port.postMessage({
            type: "CONFIGURE",
            data: { isRecording: true, isMuted: false },
          });

          return true;
        } catch (error) {
          log(`Enhanced processor test failed: ${error.message}`, "error");
          updateStatus(
            "audioWorkletStatus",
            `Processor test failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      // Test 5: Buffer Management
      async function testBufferManagement() {
        log("Testing buffer management...", "info");
        try {
          // Simulate buffer operations
          const bufferSize = 4096;
          const testBuffer = new Float32Array(bufferSize);

          // Fill buffer with test data
          for (let i = 0; i < bufferSize; i++) {
            testBuffer[i] = Math.sin((2 * Math.PI * 440 * i) / 16000) * 0.5;
          }

          // Test buffer metrics
          let overruns = 0;
          let underruns = 0;
          let fillLevel = 0.75; // 75% full

          // Simulate buffer stress test
          for (let i = 0; i < 100; i++) {
            if (Math.random() > 0.9) overruns++;
            if (Math.random() > 0.95) underruns++;
          }

          document.getElementById("bufferFillLevel").textContent = Math.round(
            fillLevel * 100
          );
          document.getElementById("bufferOverruns").textContent = overruns;
          document.getElementById("bufferUnderruns").textContent = underruns;

          log(
            `Buffer test completed: ${overruns} overruns, ${underruns} underruns`,
            "success"
          );
          updateStatus(
            "bufferStatus",
            "Buffer management test completed",
            "success"
          );
          return true;
        } catch (error) {
          log(`Buffer management test failed: ${error.message}`, "error");
          updateStatus("bufferStatus", `Failed: ${error.message}`, "error");
          return false;
        }
      }

      // Test 6: Network Resilience
      async function testNetworkResilience() {
        log("Testing network resilience...", "info");
        try {
          // Test connection quality
          const startTime = performance.now();

          if (!testWebSocket || testWebSocket.readyState !== WebSocket.OPEN) {
            const wsOk = await testWebSocketConnection();
            if (!wsOk) return false;
          }

          // Send ping message
          testWebSocket.send("PING");

          return new Promise((resolve) => {
            const timeout = setTimeout(() => {
              log("Network resilience test timeout", "warning");
              updateStatus(
                "networkStatus",
                "Network test timeout - possible latency issues",
                "warning"
              );
              resolve(false);
            }, 3000);

            const originalOnMessage = testWebSocket.onmessage;
            testWebSocket.onmessage = (event) => {
              if (
                typeof event.data === "string" &&
                event.data.includes("PONG")
              ) {
                clearTimeout(timeout);
                const latency = performance.now() - startTime;
                log(`Network latency: ${latency.toFixed(2)}ms`, "success");
                updateStatus(
                  "networkStatus",
                  `Network resilience test passed (${latency.toFixed(2)}ms)`,
                  "success"
                );
                testWebSocket.onmessage = originalOnMessage;
                resolve(true);
              } else if (originalOnMessage) {
                originalOnMessage(event);
              }
            };
          });
        } catch (error) {
          log(`Network resilience test failed: ${error.message}`, "error");
          updateStatus("networkStatus", `Failed: ${error.message}`, "error");
          return false;
        }
      }

      // Test 7: Comprehensive Integration Test
      async function runComprehensiveTest() {
        log("Starting comprehensive integration test...", "info");
        updateStatus(
          "integrationStatus",
          "Running comprehensive test...",
          "info"
        );

        const results = {
          audioContext: false,
          audioOutput: false,
          microphoneAccess: false,
          microphoneRecording: false,
          webSocketConnection: false,
          audioTransmission: false,
          audioWorkletSupport: false,
          enhancedProcessor: false,
          bufferManagement: false,
          networkResilience: false,
        };

        try {
          // Run all tests in sequence
          results.audioContext = await testAudioContext();
          results.audioOutput = await testAudioOutput();
          results.microphoneAccess = await testMicrophoneAccess();
          results.microphoneRecording = await testMicrophoneRecording();
          results.webSocketConnection = await testWebSocketConnection();
          results.audioTransmission = await testAudioTransmission();
          results.audioWorkletSupport = await testAudioWorkletSupport();
          results.enhancedProcessor = await testEnhancedAudioProcessor();
          results.bufferManagement = await testBufferManagement();
          results.networkResilience = await testNetworkResilience();

          // Calculate overall score
          const totalTests = Object.keys(results).length;
          const passedTests = Object.values(results).filter(
            (result) => result === true
          ).length;
          const score = Math.round((passedTests / totalTests) * 100);

          log(
            `Integration test completed: ${passedTests}/${totalTests} tests passed (${score}%)`,
            score >= 80 ? "success" : score >= 60 ? "warning" : "error"
          );

          // Generate detailed report
          const failedTests = Object.entries(results)
            .filter(([test, result]) => !result)
            .map(([test]) => test);

          if (failedTests.length > 0) {
            log(`Failed tests: ${failedTests.join(", ")}`, "warning");
          }

          updateStatus(
            "integrationStatus",
            `Integration test completed: ${score}% (${passedTests}/${totalTests})`,
            score >= 80 ? "success" : score >= 60 ? "warning" : "error"
          );

          return score >= 80;
        } catch (error) {
          log(`Integration test failed: ${error.message}`, "error");
          updateStatus(
            "integrationStatus",
            `Integration test failed: ${error.message}`,
            "error"
          );
          return false;
        }
      }

      // Auto-run basic tests on page load
      window.addEventListener("load", () => {
        log("Audio diagnostic test page loaded", "info");
        log("Click test buttons to diagnose audio issues", "info");
      });
    </script>
  </body>
</html>
